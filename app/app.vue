<template>
  <div class="min-h-screen bg-white text-slate-900">
    <NuxtRouteAnnouncer />
    <div class="max-w-md mx-auto min-h-screen flex flex-col">
      <header class="px-4 py-3 sticky top-0 bg-white/80 backdrop-blur border-b">
        <h1 class="text-lg font-semibold">Conjugue un verbe</h1>
      </header>
      <main class="flex-1">
        <NuxtPage />
      </main>
      <BottomNav />
    </div>
  </div>
</template>

<script setup lang="ts">
// Root layout for mobile-first app
import BottomNav from '~/components/BottomNav.vue'
</script>

<style>
:root { color-scheme: light; }
* { box-sizing: border-box; }
body { margin: 0; }
.min-h-screen { min-height: 100vh; }
.max-w-md { max-width: 420px; }
.mx-auto { margin-left: auto; margin-right: auto; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-1 { flex: 1 1 auto; }
.sticky { position: sticky; }
.top-0 { top: 0; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-3 { padding-top: .75rem; padding-bottom: .75rem; }
.border-b { border-bottom: 1px solid #e5e7eb; }
.text-lg { font-size: 1.125rem; }
.font-semibold { font-weight: 600; }
.bg-white{ background:#fff; }
.text-slate-900{ color:#0f172a; }
.bg-white\/80{ background:rgba(255,255,255,.8); }
.backdrop-blur{ backdrop-filter: blur(8px); }
</style>
