export type Person = 'je' | 'tu' | 'il' | 'elle' | 'nous' | 'vous' | 'ils' | 'elles'

export type Tense = 'present'

export interface VerbEntry {
  id: string
  infinitive: string
  emoji: string
  present: Record<Person, string>
}

// Exactly 3 starter verbs
export const STARTER_VERBS: VerbEntry[] = [
  {
    id: 'parler',
    infinitive: 'Parler',
    emoji: '💬',
    present: {
      je: 'je parle',
      tu: 'tu parles',
      il: 'il parle',
      elle: 'elle parle',
      nous: 'nous parlons',
      vous: 'vous parlez',
      ils: 'ils parlent',
      elles: 'elles parlent'
    }
  },
  {
    id: 'danser',
    infinitive: 'Danser',
    emoji: '🩰',
    present: {
      je: 'je danse',
      tu: 'tu danses',
      il: 'il danse',
      elle: 'elle danse',
      nous: 'nous dansons',
      vous: 'vous dansez',
      ils: 'ils dansent',
      elles: 'elles dansent'
    }
  },
  {
    id: 'chanter',
    infinitive: 'Chanter',
    emoji: '🎤',
    present: {
      je: 'je chante',
      tu: 'tu chantes',
      il: 'il chante',
      elle: 'elle chante',
      nous: 'nous chantons',
      vous: 'vous chantez',
      ils: 'ils chantent',
      elles: 'elles chantent'
    }
  }
]

