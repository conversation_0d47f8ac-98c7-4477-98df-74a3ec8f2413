<template>
  <section class="p-4 space-y-4">
    <h2 class="h">Conjugue un verbe</h2>
    <div class="grid">
      <div v-for="v in verbs" :key="v.id" class="inline-block mr-3 mb-3">
        <NuxtLink :to="isUnlocked(v.id) ? `/learn/${v.id}` : ''" :aria-disabled="!isUnlocked(v.id)" :tabindex="isUnlocked(v.id)? 0 : -1">
          <VerbCard :title="isUnlocked(v.id) ? v.infinitive : '?'" :icon="isUnlocked(v.id) ? v.emoji : '🔒'" :progress="progress[v.id]?.correct || 0" :current="8" :locked="!isUnlocked(v.id)" />
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { STARTER_VERBS } from '~/app/utils/verbs'
import { useProgressStore } from '~/app/stores/progress'
import VerbCard from '~/components/VerbCard.vue'

const verbs = STARTER_VERBS
const store = useProgressStore()
await store.load()

const isUnlocked = store.isUnlocked
const progress = store.verbs
</script>

<style scoped>
.p-4{padding:1rem}
.h{ font-size:18px; font-weight:700; }
.space-y-4 > * + *{margin-top:1rem}
.grid{display:flex; flex-wrap:wrap}
.inline-block{display:inline-block}
.mr-3{margin-right:.75rem}
.mb-3{margin-bottom:.75rem}
[aria-disabled="true"]{ pointer-events:none; }
</style>

