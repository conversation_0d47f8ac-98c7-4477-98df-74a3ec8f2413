<template>
  <section class="p-4">
    <NuxtLink to="/" class="back">← Niveau</NuxtLink>
    <div class="card">
      <div class="header">
        <div class="emoji">{{ verb.emoji }}</div>
        <div class="name">{{ verb.infinitive }}</div>
        <div class="tag">présent</div>
      </div>
      <div class="subtitle">Complète le verbe</div>
      <div class="grid">
        <button v-for="(q,i) in questions" :key="i" class="btn" :class="{ ok: answers[i], wrong: answers[i]===false }" @click="toggle(i)">
          {{ showAnswers ? q.full : q.mask }}
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { STARTER_VERBS, type VerbEntry, type Person } from '~/app/utils/verbs'
import { useProgressStore } from '~/app/stores/progress'
import { ref, reactive } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const id = String(route.params.verb)
const verb = STARTER_VERBS.find(v => v.id === id) as VerbEntry

const store = useProgressStore()
store.startRound(verb.id)

const persons: Person[] = ['je','tu','il','elle','nous','vous','ils','elles']

const questions = persons.map((p) => {
  const full = verb.present[p]
  // very small mask: replace vowel cluster of verb ending
  const mask = full.replace(/([a-z])([a-z]+)$/i, '$1_'+ '_'.repeat(2))
  return { p, full, mask }
})

const answers = reactive<(boolean|undefined)[]>(Array(questions.length).fill(undefined))
const showAnswers = ref(false)

function toggle(index: number){
  if (answers[index] !== undefined) return
  // mark as correct in demo; tap again reveals full answers
  answers[index] = true
  store.recordAnswer(verb.id, true)
  if (answers.every(a => a === true)) {
    showAnswers.value = true
  }
}
</script>

<style scoped>
.p-4{padding:1rem}
.back{color:#111827; text-decoration:none;}
.card{ margin-top:.75rem; }
.header{ display:flex; align-items:center; gap:.75rem; }
.emoji{ font-size:28px; }
.name{ font-size:22px; font-weight:700; background:#ede9fe; padding:.25rem .5rem; border-radius:10px; }
.tag{ background:#f472b6; color:white; padding:.2rem .5rem; border-radius:8px; font-size:12px; }
.subtitle{ margin:.75rem 0; font-weight:600; }
.grid{ display:grid; grid-template-columns: 1fr 1fr; gap:.75rem; }
.btn{ background:#f3f4f6; border-radius:12px; padding:.75rem; border:none; text-align:left; font-size:16px; }
.btn.ok{ outline:2px solid #34d399 }
.btn.wrong{ outline:2px solid #ef4444 }
</style>

