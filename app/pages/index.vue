<template>
  <section class="p-4 space-y-4">
    <div class="grid">
      <div v-for="v in verbs" :key="v.id" class="inline-block mr-3 mb-3">
        <NuxtLink :to="`/learn/${v.id}`" v-if="isUnlocked(v.id)">
          <VerbCard :title="v.infinitive" :icon="v.emoji" :progress="progress[v.id]?.correct || 0" :current="8" />
        </NuxtLink>
        <div v-else>
          <VerbCard :title="'?'" :icon="'🔒'" :progress="0" :current="8" :locked="true" />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { STARTER_VERBS } from '~/app/utils/verbs'
import { useProgressStore } from '~/app/stores/progress'
import VerbCard from '~/components/VerbCard.vue'

const verbs = STARTER_VERBS
const store = useProgressStore()
await store.load()

const isUnlocked = store.isUnlocked
const progress = store.verbs
</script>

<style scoped>
.p-4{padding:1rem}
.space-y-4 > * + *{margin-top:1rem}
.grid{display:flex; flex-wrap:wrap}
.inline-block{display:inline-block}
.mr-3{margin-right:.75rem}
.mb-3{margin-bottom:.75rem}
</style>

