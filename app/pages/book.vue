<template>
  <section class="p-4 space-y-3">
    <h2 class="h">Mon carnet de conjugaison</h2>
    <div v-for="v in mastered" :key="v.id" class="row">
      <div class="emoji">{{ v.emoji }}</div>
      <div class="name">{{ v.infinitive }}</div>
      <NuxtLink class="review" :to="`/learn/${v.id}`">Réviser</NuxtLink>
    </div>
    <div v-if="!mastered.length" class="muted">Maîtrise un verbe pour le retrouver ici.</div>
  </section>
</template>

<script setup lang="ts">
import { STARTER_VERBS } from '~/app/utils/verbs'
import { useProgressStore } from '~/app/stores/progress'

const store = useProgressStore()
await store.load()
const mastered = computed(() => STARTER_VERBS.filter(v => store.verbs[v.id].mastered))
</script>

<style scoped>
.p-4{padding:1rem}
.space-y-3>*+*{margin-top:.75rem}
.h{ font-size:18px; font-weight:700; }
.row{ display:flex; align-items:center; gap:.75rem; background:#fff7ed; border-radius:12px; padding:.75rem; }
.emoji{ font-size:22px; }
.name{ font-weight:700; }
.review{ margin-left:auto; text-decoration:none; color:#111827; background:#fde68a; padding:.35rem .6rem; border-radius:8px; }
.muted{ color:#6b7280; font-size:14px; }
</style>

