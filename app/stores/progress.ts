import { defineStore } from 'pinia'
import { STARTER_VERBS } from '~/app/utils/verbs'

export interface VerbProgress {
  id: string
  // Attempts in current learning round
  correct: number
  total: number
  mastered: boolean
}

export interface UserState {
  verbs: Record<string, VerbProgress>
  unlockedOrder: string[] // progressive unlocking order
}

const TOTAL_ITEMS = 8 // present persons

export const useProgressStore = defineStore('progress', {
  state: (): UserState => ({
    verbs: Object.fromEntries(
      STARTER_VERBS.map(v => [v.id, { id: v.id, correct: 0, total: 0, mastered: false }])
    ),
    unlockedOrder: [STARTER_VERBS[0].id] // Start with first verb only
  }),
  getters: {
    isUnlocked: (s) => (id: string) => s.unlockedOrder.includes(id),
  },
  actions: {
    async load() {
      try {
        const data = await $fetch('/api/progress')
        if (data?.verbs) Object.assign(this.$state, data)
      } catch (e) {
        // ignore
      }
    },
    async save() {
      try { await $fetch('/api/progress', { method: 'POST', body: this.$state }) } catch {}
    },
    startRound(verbId: string) {
      const vp = this.verbs[verbId]
      vp.correct = 0
      vp.total = 0
    },
    recordAnswer(verbId: string, correct: boolean) {
      const vp = this.verbs[verbId]
      vp.total += 1
      if (correct) vp.correct += 1
      // mastery rule: 8 correct with no mistake in current round => mastered
      if (!vp.mastered && vp.correct >= TOTAL_ITEMS && vp.correct === vp.total) {
        vp.mastered = true
        // unlock next verb
        const idx = STARTER_VERBS.findIndex(v => v.id === verbId)
        const next = STARTER_VERBS[idx + 1]
        if (next && !this.unlockedOrder.includes(next.id)) this.unlockedOrder.push(next.id)
      }
      this.save()
    },
    resetProgress() {
      this.$reset()
      this.save()
    }
  }
})

