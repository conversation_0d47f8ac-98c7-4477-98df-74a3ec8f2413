{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/image": "1.11.0", "@nuxt/test-utils": "3.19.2", "@nuxthub/core": "^0.9.0", "@pinia/nuxt": "0.11.2", "@una-ui/nuxt": "^1.0.0-alpha.9", "@vueuse/nuxt": "13.8.0", "nuxt": "^4.0.3", "nuxt-auth-utils": "0.5.23", "pinia": "^3.0.3", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/lucide": "^1.2.65", "@iconify-json/ph": "^1.2.2", "@iconify-json/radix-icons": "^1.2.4", "@iconify-json/solar": "^1.2.4", "@iconify-json/tabler": "^1.2.22", "wrangler": "^4.33.1"}}