// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  modules: [
    '@nuxt/test-utils',
    '@nuxt/image',
    'nuxt-auth-utils',
    '@vueuse/nuxt',
    '@pinia/nuxt',
    '@nuxthub/core',
    '@una-ui/nuxt'
  ],
  // NuxtHub features we will use locally and on Cloudflare
  hub: {
    kv: true
  },
  app: {
    head: {
      title: 'FrenchClass - Conjugaison',
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1' }
      ]
    }
  }
})