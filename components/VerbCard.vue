<template>
  <UCard class="card" :class="locked ? 'card-locked' : ''">
    <div class="title">{{ title }}</div>
    <div class="icon">{{ icon }}</div>
    <div class="progress">
      <div class="bar" :style="{ width: `${Math.round((progress/current)*100)}%` }" />
      <span class="txt">{{ progress }}/{{ current }}</span>
    </div>
  </UCard>
</template>

<script setup lang="ts">
defineProps<{ title: string; progress: number; current: number; icon?: string; locked?: boolean }>()
</script>

<style scoped>
.card { background: #ede9fe; border-radius: 16px; padding: 14px; min-width: 140px; position: relative; display:block; }
.card-locked { opacity: .6; filter: grayscale(1); }
.title { font-size: 22px; font-weight: 700; margin-bottom: 6px; }
.icon { font-size: 34px; }
.progress{ display:flex; align-items:center; gap:6px; margin-top:8px; }
.bar{ height:6px; background:#c4b5fd; border-radius:999px; flex:1; position:relative; }
.txt{ font-size:12px; color:#4b5563; }
</style>

