<template>
  <nav class="sticky bottom-0 border-t bg-amber-100">
    <div class="max-w-md mx-auto grid grid-cols-4 gap-2 px-6 py-3 text-center">
      <NuxtLink class="icon" to="/">
        <span>🏠</span>
      </NuxtLink>
      <NuxtLink class="icon" to="/learn">
        <span>🚶</span>
      </NuxtLink>
      <NuxtLink class="icon" to="/book">
        <span>📒</span>
      </NuxtLink>
      <NuxtLink class="icon" to="/settings">
        <span>⚙️</span>
      </NuxtLink>
    </div>
  </nav>
</template>

<style scoped>
.border-t { border-top: 1px solid #e5e7eb; }
.bg-amber-100 { background: #fde68a; }
.grid { display: grid; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-2 { gap: .5rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-3 { padding-top: .75rem; padding-bottom: .75rem; }
.text-center { text-align: center; }
.icon { display: inline-flex; align-items:center; justify-content:center; width: 42px; height: 32px; border-radius: 10px; color: #111; text-decoration: none; }
.icon.router-link-active { background: #111827; color: white; }
</style>

